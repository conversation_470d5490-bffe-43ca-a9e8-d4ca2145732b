use clap::{Arg, Command};

fn main() {
    // Process arguments using clap
    // The commands should reflect the REST API endpoints in the colony-daemon as follows:

    // colony-cli refresh
       // Calls the POST /api/v1/jobs/cache/refresh endpoint
       // Waits for the command to complete and prints the result
    // colony-cli refresh --depth <depth>
       // Calls the POST /api/v1/jobs/cache/refresh/{depth} endpoint
       // Waits for the command to complete and prints the result
    // colony-cli upload
       // Calls the POST /api/v1/jobs/cache/upload endpoint
       // Waits for the command to complete and prints the result
    // colony-cli upload <pod>
       // Calls the POST /api/v1/jobs/cache/upload/{pod} endpoint
       // Waits for the command to complete and prints the result
    // colony-cli search sparql <query>
       // Calls the POST /api/v1/jobs/search endpoint with a JSON field "type" set to "advanced
       // and "sparql"containing the SPARQL query
       // Waits for the command to complete and prints the result
    // colony-cli search text <query> --limit <limit>
       // Calls the POST /api/v1/jobs/search endpoint with a JSON field "type" set to "text"
       // and "text" containing the search term
       // the optional --limit flag is passed as a JSON field "limit", if not passed defaults to 50
       // Waits for the command to complete and prints the result
    // colony-cli search type <type> --limit <limit>
       // Calls the POST /api/v1/jobs/search endpoint with a JSON field "type" set to "by-type"
       // and "by_type" containing the type name
       // the optional --limit flag is passed as a JSON field "limit", if not passed defaults to 50
       // Waits for the command to complete and prints the result
    // colony-cli search predicate <predicate> --limit <limit>
       // Calls the POST /api/v1/jobs/search endpoint with a JSON field "type" set to "by-predicate"
       // and "by_predicate" containing the predicate name
       // the optional --limit flag is passed as a JSON field "limit", if not passed defaults to 50
       // Waits for the command to complete and prints the result
    // colony-cli search subject <subject>
       // Calls the POST /api/v1/jobs/search/subject/{subject} endpoint
       // Waits for the command to complete and prints the result
    // colony-cli pods
       // Calls the GET /api/v1/pods endpoint
       // Prints the list of pods
    // colony-cli add pod <name>
       // Calls the POST /api/v1/pods endpoint
       // Prints the pod response
    // colony-cli add ref <pod> <ref>
       // Calls the POST /api/v1/pods/{pod}/pod_ref endpoint
       // Prints the result
    // colony-cli rm ref <pod> <ref>
       // Calls the DELETE /api/v1/pods/{pod}/pod_ref endpoint
       // Prints the result
    // colony-cli put <pod> <subject> <data>
       // Calls the PUT /api/v1/pods/{pod}/{subject} endpoint and passes the data as JSON
       // Prints the result
    

}
